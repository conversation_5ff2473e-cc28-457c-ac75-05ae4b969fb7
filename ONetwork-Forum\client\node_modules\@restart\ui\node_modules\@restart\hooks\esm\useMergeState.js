import { useCallback, useState } from 'react';

/**
 * Updates state, partial updates are merged into existing state values
 */

/**
 * Mimics a React class component's state model, of having a single unified
 * `state` object and an updater that merges updates into the existing state, as
 * opposed to replacing it.
 *
 * ```js
 * const [state, setState] = useMergeState({ name: '<PERSON>', age: 24 })
 *
 * setState({ name: '<PERSON>' }) // { name: '<PERSON>', age: 24 }
 *
 * setState(state => ({ age: state.age + 10 })) // { name: '<PERSON>', age: 34 }
 * ```
 *
 * @param initialState The initial state object
 */
export default function useMergeState(initialState) {
  const [state, setState] = useState(initialState);
  const updater = useCallback(update => {
    if (update === null) return;
    if (typeof update === 'function') {
      setState(state => {
        const nextState = update(state);
        return nextState == null ? state : Object.assign({}, state, nextState);
      });
    } else {
      setState(state => Object.assign({}, state, update));
    }
  }, [setState]);
  return [state, updater];
}