import { placements } from '@popperjs/core/lib/enums';
export declare const createPopper: <TModifier extends Partial<import("@popperjs/core/lib/types").Modifier<any, any>>>(reference: Element | import("@popperjs/core/lib/types").VirtualElement, popper: HTMLElement, options?: Partial<import("@popperjs/core/lib/types").OptionsGeneric<TModifier>> | undefined) => import("@popperjs/core/lib/types").Instance;
export { placements };
