import React, { useState } from 'react'
import { Typo<PERSON>, Button } from '@mui/material'

const Statebasic = () => {

  var [fname, setFname] = useState("king");
  const change = () => {
    setFname("jocker");
  }
  return (
    <div>
      <Typography variant="h3" color="error">WELCOME!!</Typography>
      <Typography variant="h5">Hello, {fname}</Typography>
      <Button variant="contained" onClick={() => { setFname("queen") }}>Change Name</Button>
      <br />
      <br />
      <Button variant="contained" onClick={change} >Change Name</Button>
    </div>
  )
}

export default Statebasic