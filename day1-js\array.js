// let fruits=["apple","banana","orange",12];
// console.log(fruits);
// console.log(fruits[0]);
// console.log(fruits[1]);
// console.log(fruits[2]);
// console.log(fruits[3]);
// fruits[4]="kiwi";
// console.log(fruits);
// fruits.push("mango");
// console.log(fruits);
// fruits.pop();
// console.log(fruits);
// fruits.shift();
// console.log(fruits);
// fruits.unshift("pineapple");
// console.log(fruits);

//array of objects
var openbatch = [
    {name: "<PERSON>", age: 25, place: "kerala"},
    {name: "<PERSON>", age: 30, place: "tamil nadu"},
    {name: "<PERSON>", age: 28, place: "karnataka"}
];
console.log(openbatch);
console.log(openbatch[0]);
console.log(openbatch[1]);
console.log(openbatch[2]);
console.log(openbatch[0].name);
console.log(openbatch[0].age);
console.log(openbatch[0].place);
console.log(openbatch[1].name);
console.log(openbatch[1].age);
console.log(openbatch[1].place);
console.log(openbatch[2].name);
console.log(openbatch[2].age);
console.log(openbatch[2].place);