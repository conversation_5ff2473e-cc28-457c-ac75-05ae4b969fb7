import React from 'react'
import './App.css'
import Navbar from './components/Navbar'
import { Routes, Route } from 'react-router-dom'
import First from './components/First'
import Register from './components/Register'
import Datatable from './components/Datatable'
import Datacard from './components/Datacard'
import Statebasic from './components/Statebasic'
import Task from './components/Task'
import Counter from './components/Counter'
import Inputdata from './components/Inputdata'

function App() {
  
  return (
    <>
      <Navbar />
      <Routes>
        <Route path="/" element={<First />} />
        <Route path="/register" element={<Register />} />
        <Route path="/datatable" element={<Datatable />} />
        <Route path="/datacard" element={<Datacard />} />
        <Route path="/statebasic" element={<Statebasic />} />
        <Route path="/task" element={<Task />} />
        <Route path='/counter' element={<Counter />} />
        <Route path='/inputdata' element={<Inputdata />} />
      </Routes>
    </>
  );
}

export default App
