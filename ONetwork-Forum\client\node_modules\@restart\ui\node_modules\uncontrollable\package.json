{"name": "uncontrollable", "version": "8.0.4", "description": "Wrap a controlled react component, to allow specific prop/handler pairs to be uncontrolled", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/jquense/uncontrollable.git"}, "license": "MIT", "main": "lib/cjs/index.js", "module": "lib/esm/index.js", "keywords": ["uncontrolled-component", "react-component", "input", "controlled", "uncontrolled", "form"], "scripts": {"test": "jest", "tdd": "jest --watch", "build": "build src", "prepublishOnly": "npm run build", "release": "rollout --conventionalCommits"}, "prettier": {"singleQuote": true}, "peerDependencies": {"react": ">=16.14.0"}, "jest": {"testEnvironment": "jsdom"}, "devDependencies": {"@4c/build": "^4.0.0", "@4c/rollout": "^4.0.2", "@4c/tsconfig": "^0.4.1", "@types/react": ">=18.0.28", "@babel/cli": "^7.21.0", "@babel/core": "^7.21.0", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.0", "@testing-library/react": "^14.0.0", "babel-jest": "^29.5.0", "babel-preset-env-modules": "^1.0.1", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^4.9.5"}, "bugs": {"url": "https://github.com/jquense/uncontrollable/issues"}, "homepage": "https://github.com/jquense/uncontrollable#readme"}