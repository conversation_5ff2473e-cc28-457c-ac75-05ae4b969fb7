import React from 'react'
import { Ty<PERSON><PERSON>, <PERSON>Field, But<PERSON> } from '@mui/material'
import '../App.css'
import Navbar from './Navbar'

const Register = () => {
  return (
    <div className="box">
      <Typography variant="h1" color="error">
        Register to HELL!!!
      </Typography>
      <br />
      <TextField error label="Name" variant="outlined" />
      <br />
      <br />
      <TextField error label="Place" variant="outlined" />
      <br />
      <br />
      <TextField error label="Age" variant="outlined" type="number" />
      <br />
      <br />
      <TextField error label="Username" variant="outlined" />
      <br />
      <br />
      <TextField error label="Password" variant="outlined" type="password" />
      <br />
      <br />
      <Button
        variant="outlined"
        color="error"
        size="large"
        onClick={() => alert("You Are Registered!")}
      >
        Register
      </Button>
    </div>
  );
}

export default Register