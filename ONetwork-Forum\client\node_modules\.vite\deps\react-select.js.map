{"version": 3, "sources": ["../../react-select/dist/react-select.esm.js"], "sourcesContent": ["import { u as useStateManager } from './useStateManager-7e1e8489.esm.js';\nexport { u as useStateManager } from './useStateManager-7e1e8489.esm.js';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { forwardRef, useMemo } from 'react';\nimport { S as Select } from './Select-aab027f3.esm.js';\nexport { c as createFilter, d as defaultTheme, m as mergeStyles } from './Select-aab027f3.esm.js';\nimport { CacheProvider } from '@emotion/react';\nimport createCache from '@emotion/cache';\nexport { c as components } from './index-641ee5b8.esm.js';\nimport '@babel/runtime/helpers/objectSpread2';\nimport '@babel/runtime/helpers/slicedToArray';\nimport '@babel/runtime/helpers/objectWithoutProperties';\nimport '@babel/runtime/helpers/classCallCheck';\nimport '@babel/runtime/helpers/createClass';\nimport '@babel/runtime/helpers/inherits';\nimport '@babel/runtime/helpers/createSuper';\nimport '@babel/runtime/helpers/toConsumableArray';\nimport 'memoize-one';\nimport '@babel/runtime/helpers/typeof';\nimport '@babel/runtime/helpers/taggedTemplateLiteral';\nimport '@babel/runtime/helpers/defineProperty';\nimport 'react-dom';\nimport '@floating-ui/dom';\nimport 'use-isomorphic-layout-effect';\n\nvar StateManagedSelect = /*#__PURE__*/forwardRef(function (props, ref) {\n  var baseSelectProps = useStateManager(props);\n  return /*#__PURE__*/React.createElement(Select, _extends({\n    ref: ref\n  }, baseSelectProps));\n});\nvar StateManagedSelect$1 = StateManagedSelect;\n\nvar NonceProvider = (function (_ref) {\n  var nonce = _ref.nonce,\n    children = _ref.children,\n    cacheKey = _ref.cacheKey;\n  var emotionCache = useMemo(function () {\n    return createCache({\n      key: cacheKey,\n      nonce: nonce\n    });\n  }, [cacheKey, nonce]);\n  return /*#__PURE__*/React.createElement(CacheProvider, {\n    value: emotionCache\n  }, children);\n});\n\nexport { NonceProvider, StateManagedSelect$1 as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,YAAuB;AACvB,mBAAoC;AAkBpC,uBAAO;AAIP,IAAI,yBAAkC,yBAAW,SAAU,OAAO,KAAK;AACrE,MAAI,kBAAkB,gBAAgB,KAAK;AAC3C,SAA0B,oBAAc,QAAQ,SAAS;AAAA,IACvD;AAAA,EACF,GAAG,eAAe,CAAC;AACrB,CAAC;AACD,IAAI,uBAAuB;AAE3B,IAAI,gBAAiB,SAAU,MAAM;AACnC,MAAI,QAAQ,KAAK,OACf,WAAW,KAAK,UAChB,WAAW,KAAK;AAClB,MAAI,mBAAe,sBAAQ,WAAY;AACrC,WAAO,YAAY;AAAA,MACjB,KAAK;AAAA,MACL;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,UAAU,KAAK,CAAC;AACpB,SAA0B,oBAAc,eAAe;AAAA,IACrD,OAAO;AAAA,EACT,GAAG,QAAQ;AACb;", "names": []}