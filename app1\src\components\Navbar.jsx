import React from "react";
import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import Toolbar from "@mui/material/Toolbar";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import MenuIcon from "@mui/icons-material/Menu";
import { Link } from "react-router-dom";
const Navbar = () => {
  return (
    <div>
      <Box sx={{ flexGrow: 1 }}>
        <AppBar position="static" style={{ textAlign: "left" }}>
          <Toolbar>
            <IconButton
              size="large"
              edge="start"
              color="inherit"
              aria-label="menu"
              sx={{ mr: 2 }}
            >
              <MenuIcon /> &nbsp;
              <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                AppBar
              </Typography>
            </IconButton>
            <IconButton>
              <Link to="/" style={{ textDecoration: "none", color: "white" }}>
                Home
              </Link>
            </IconButton>
            <IconButton>
              <Link
                to="/register"
                style={{ textDecoration: "none", color: "white" }}
              >
                Register
              </Link>
            </IconButton>
            <IconButton>
              <Link
                to="/datatable"
                style={{ textDecoration: "none", color: "white" }}
              >
                Data table
              </Link>
            </IconButton>
            <IconButton>
              <Link
                to="/datacard"
                style={{ textDecoration: "none", color: "white" }}
              >
                Data Card
              </Link>
            </IconButton>
            <IconButton>
              <Link
                to="/statebasic"
                style={{ textDecoration: "none", color: "white" }}
              >
                State Basic
              </Link>
            </IconButton>
            <IconButton>
              <Link
                to="/task"
                style={{ textDecoration: "none", color: "white" }}
              >
                Task
              </Link>
            </IconButton>
            <IconButton>
              <Link
                to="/counter"
                style={{ textDecoration: "none", color: "white" }}
              >
                Counter
              </Link>
            </IconButton>
              <IconButton>
              <Link
                to="/inputdata"
                style={{ textDecoration: "none", color: "white" }}
              >
                Input Data
              </Link>
            </IconButton>
          </Toolbar>
        </AppBar>
      </Box>
    </div>
  );
};

export default Navbar;
