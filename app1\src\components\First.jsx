import React from "react";
import LoginIcon from "@mui/icons-material/Login";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import Navbar from "./Navbar";

const First = () => {
  return (
    <div className="box">
      <h1>Welcome to HELL!!</h1>
      <input type="email" id="email" placeholder="Enter your email" />
      <br />
      <input type="password" id="password" placeholder="Enter your password" />
      <br />
      <br />
      <LoginIcon onClick={() => alert("You Are Gonna Die!")} fontSize="large" />
      <Typography variant="h1" color="error">
        Login to HELL!!!
      </Typography>
      <br />
      <TextField error label="Name" variant="outlined" />
      <br />
      <br />
      <TextField error label="Email" variant="filled" type="email" />
      <br />
      <br />
      <TextField error label="Password" variant="standard" type="password" />
      <br />
      <br />
      <Button
        variant="outlined"
        color="error"
        size="large"
        onClick={() => alert("You Are Gonna Die!")}
      >
        Login
      </Button>
      &nbsp;
      <Button
        variant="text"
        color="error"
        size="large"
        onClick={() => alert("You Are Gonna Live!")}
      >
        Login
      </Button>
      &nbsp;
      <Button
        variant="contained"
        color="error"
        size="large"
        onClick={() => alert("You Are Gonna Die!")}
      >
        Login
      </Button>
    </div>
  );
};

export default First;
