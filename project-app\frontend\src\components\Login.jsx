import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>ield, Typography, Paper } from "@mui/material";
import { useState } from "react";
import axios from "axios";
import { useNavigate } from "react-router-dom";

const Login = () => {
  var navigate = useNavigate();
  var [data, setData] = useState({ email: "", password: "" });

  var inputHandler = (e) => {
    setData({ ...data, [e.target.name]: e.target.value });
    console.log(data);
  };

  var submitHandler = () => {
    console.log("login data", data);
    axios
      .post("http://localhost:3000/login", data)
      .then((response) => {
        console.log("Login successful:", response.data);
        if (response.data.token) {
          localStorage.setItem('token', response.data.token);
          localStorage.setItem('user', JSON.stringify(response.data.user));
          alert("Login successful!");
          navigate("/");
        } else {
          alert(response.data);
        }
      })
      .catch((error) => {
        console.error("Error logging in:", error);
        alert("Error logging in");
      });
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'center', marginTop: '50px' }}>
      <Paper style={{ padding: '30px', maxWidth: '400px', width: '100%' }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Login
        </Typography>
        <br />
        <TextField
          id="email"
          label="Email"
          variant="outlined"
          name="email"
          type="email"
          value={data.email}
          onChange={inputHandler}
          fullWidth
          margin="normal"
        />
        <TextField
          id="password"
          label="Password"
          variant="outlined"
          type="password"
          name="password"
          value={data.password}
          onChange={inputHandler}
          fullWidth
          margin="normal"
        />
        <br />
        <br />
        <Button 
          variant="contained" 
          color="primary" 
          onClick={submitHandler}
          fullWidth
        >
          Login
        </Button>
        <br />
        <br />
        <Typography variant="body2">
          Don't have an account? <a href="/register">Register here</a>
        </Typography>
      </Paper>
    </div>
  );
};

export default Login;
