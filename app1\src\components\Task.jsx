import { Button, Typography } from '@mui/material'
import React from 'react'

const Task = () => {
  const [fname, setFname] = React.useState("");
  return (
    <div>
      <Typography variant="h3">
        Welcome {fname}
      </Typography>
      <Button variant="outlined" onClick={() => setFname("Gallery")}>Gallery</Button> &nbsp;&nbsp;
      <Button variant="contained" onClick={() => setFname("Home")}>Home</Button> &nbsp;&nbsp;
      <Button variant="outlined" onClick={() => setFname("Contact")}>Contact</Button>
    </div>
  );
}

export default Task