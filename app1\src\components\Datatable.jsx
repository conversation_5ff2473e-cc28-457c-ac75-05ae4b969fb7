import React, { useEffect , useState} from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ield, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import Navbar from "./Navbar";
import axios from "axios";


const Datatable = () => {
  var [users, setUsers] = useState([]);
  // useEffect(() => {}, []);
  useEffect(() => {
    // axios
    //   .get("url")
    //   .then((res) => {}).catch((err) => {});
    //   );
    axios
      .get("https://jsonplaceholder.typicode.com/users")
      .then((res) => {
        setUsers(res.data);
        // console.log(res.data);
      })
      .catch((err) => {
        console.error(err);
      });
  }, []);
  return (

    <div>
      <Typography variant="h3" color="error">
        WELCOME TO Datatable
      </Typography>
      <TableContainer
        style={{
          border: "1px solid black",
          marginTop: "20px",
          marginLeft: "20px",
          marginRight: "50px",
          padding: "1px",
        }}
      >
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Name</TableCell>
              <TableCell>City</TableCell>
              <TableCell>Email</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.map((val,i) => {
              return (
              <TableRow key={i}>
                <TableCell>{val.id}</TableCell>
                <TableCell>{val.name}</TableCell>
                <TableCell>{val.address.city}</TableCell>
                <TableCell>{val.email}</TableCell>
              </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};

export default Datatable;
