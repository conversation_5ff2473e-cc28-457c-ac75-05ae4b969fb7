import { Button, TextField, Typography } from '@mui/material'
import React from 'react'
import { useState } from 'react'

const Inputdata = () => {
    var [inp,setInp] = useState();
    var [data,setData] = useState();
    const inputhandler = (e) => {

        setInp(e.target.value);

    }
  return (
    <div>
        <Typography variant="h3" color="error">
        Welcome {data}
      </Typography>
        <br /><br />
      <TextField label="Enter your name" variant="outlined" onChange={inputhandler}/>
      <br /><br />
      <Button variant="contained" color="primary" onClick={() => setData(inp)}>
        Submit
      </Button>
    </div>
  )
}

export default Inputdata