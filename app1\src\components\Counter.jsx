import { Button, Typography } from '@mui/material'
import React from 'react'
import { useState } from 'react'

const Counter = () => {
    var[count,setCount]= useState(0);
  return (
    <div>
        <Typography variant="h3" color="error">
        Counter : {count}
      </Typography>
      <Button variant="contained" color="primary" onClick={() => setCount(count + 1)}>
        +
      </Button> &nbsp;
        <Button variant="contained" color="secondary" onClick={() => setCount(count - 1)}>
            -
        </Button>
    </div>
  )
}

export default Counter