{"version": 3, "sources": ["../../react-select/creatable/dist/react-select-creatable.esm.js", "../../react-select/dist/useCreatable-84008237.esm.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { forwardRef } from 'react';\nimport { S as Select } from '../../dist/Select-aab027f3.esm.js';\nimport { u as useStateManager } from '../../dist/useStateManager-7e1e8489.esm.js';\nimport { u as useCreatable } from '../../dist/useCreatable-84008237.esm.js';\nexport { u as useCreatable } from '../../dist/useCreatable-84008237.esm.js';\nimport '@babel/runtime/helpers/objectSpread2';\nimport '@babel/runtime/helpers/classCallCheck';\nimport '@babel/runtime/helpers/createClass';\nimport '@babel/runtime/helpers/inherits';\nimport '@babel/runtime/helpers/createSuper';\nimport '@babel/runtime/helpers/toConsumableArray';\nimport '../../dist/index-641ee5b8.esm.js';\nimport '@emotion/react';\nimport '@babel/runtime/helpers/slicedToArray';\nimport '@babel/runtime/helpers/objectWithoutProperties';\nimport '@babel/runtime/helpers/typeof';\nimport '@babel/runtime/helpers/taggedTemplateLiteral';\nimport '@babel/runtime/helpers/defineProperty';\nimport 'react-dom';\nimport '@floating-ui/dom';\nimport 'use-isomorphic-layout-effect';\nimport 'memoize-one';\n\nvar CreatableSelect = /*#__PURE__*/forwardRef(function (props, ref) {\n  var creatableProps = useStateManager(props);\n  var selectProps = useCreatable(creatableProps);\n  return /*#__PURE__*/React.createElement(Select, _extends({\n    ref: ref\n  }, selectProps));\n});\nvar CreatableSelect$1 = CreatableSelect;\n\nexport { CreatableSelect$1 as default };\n", "import _objectSpread from '@babel/runtime/helpers/esm/objectSpread2';\nimport _toConsumableArray from '@babel/runtime/helpers/esm/toConsumableArray';\nimport _objectWithoutProperties from '@babel/runtime/helpers/esm/objectWithoutProperties';\nimport { useMemo, useCallback } from 'react';\nimport { H as cleanValue, D as valueTernary } from './index-641ee5b8.esm.js';\nimport { g as getOptionValue, b as getOptionLabel } from './Select-aab027f3.esm.js';\n\nvar _excluded = [\"allowCreateWhileLoading\", \"createOptionPosition\", \"formatCreateLabel\", \"isValidNewOption\", \"getNewOptionData\", \"onCreateOption\", \"options\", \"onChange\"];\nvar compareOption = function compareOption() {\n  var inputValue = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  var option = arguments.length > 1 ? arguments[1] : undefined;\n  var accessors = arguments.length > 2 ? arguments[2] : undefined;\n  var candidate = String(inputValue).toLowerCase();\n  var optionValue = String(accessors.getOptionValue(option)).toLowerCase();\n  var optionLabel = String(accessors.getOptionLabel(option)).toLowerCase();\n  return optionValue === candidate || optionLabel === candidate;\n};\nvar builtins = {\n  formatCreateLabel: function formatCreateLabel(inputValue) {\n    return \"Create \\\"\".concat(inputValue, \"\\\"\");\n  },\n  isValidNewOption: function isValidNewOption(inputValue, selectValue, selectOptions, accessors) {\n    return !(!inputValue || selectValue.some(function (option) {\n      return compareOption(inputValue, option, accessors);\n    }) || selectOptions.some(function (option) {\n      return compareOption(inputValue, option, accessors);\n    }));\n  },\n  getNewOptionData: function getNewOptionData(inputValue, optionLabel) {\n    return {\n      label: optionLabel,\n      value: inputValue,\n      __isNew__: true\n    };\n  }\n};\nfunction useCreatable(_ref) {\n  var _ref$allowCreateWhile = _ref.allowCreateWhileLoading,\n    allowCreateWhileLoading = _ref$allowCreateWhile === void 0 ? false : _ref$allowCreateWhile,\n    _ref$createOptionPosi = _ref.createOptionPosition,\n    createOptionPosition = _ref$createOptionPosi === void 0 ? 'last' : _ref$createOptionPosi,\n    _ref$formatCreateLabe = _ref.formatCreateLabel,\n    formatCreateLabel = _ref$formatCreateLabe === void 0 ? builtins.formatCreateLabel : _ref$formatCreateLabe,\n    _ref$isValidNewOption = _ref.isValidNewOption,\n    isValidNewOption = _ref$isValidNewOption === void 0 ? builtins.isValidNewOption : _ref$isValidNewOption,\n    _ref$getNewOptionData = _ref.getNewOptionData,\n    getNewOptionData = _ref$getNewOptionData === void 0 ? builtins.getNewOptionData : _ref$getNewOptionData,\n    onCreateOption = _ref.onCreateOption,\n    _ref$options = _ref.options,\n    propsOptions = _ref$options === void 0 ? [] : _ref$options,\n    propsOnChange = _ref.onChange,\n    restSelectProps = _objectWithoutProperties(_ref, _excluded);\n  var _restSelectProps$getO = restSelectProps.getOptionValue,\n    getOptionValue$1 = _restSelectProps$getO === void 0 ? getOptionValue : _restSelectProps$getO,\n    _restSelectProps$getO2 = restSelectProps.getOptionLabel,\n    getOptionLabel$1 = _restSelectProps$getO2 === void 0 ? getOptionLabel : _restSelectProps$getO2,\n    inputValue = restSelectProps.inputValue,\n    isLoading = restSelectProps.isLoading,\n    isMulti = restSelectProps.isMulti,\n    value = restSelectProps.value,\n    name = restSelectProps.name;\n  var newOption = useMemo(function () {\n    return isValidNewOption(inputValue, cleanValue(value), propsOptions, {\n      getOptionValue: getOptionValue$1,\n      getOptionLabel: getOptionLabel$1\n    }) ? getNewOptionData(inputValue, formatCreateLabel(inputValue)) : undefined;\n  }, [formatCreateLabel, getNewOptionData, getOptionLabel$1, getOptionValue$1, inputValue, isValidNewOption, propsOptions, value]);\n  var options = useMemo(function () {\n    return (allowCreateWhileLoading || !isLoading) && newOption ? createOptionPosition === 'first' ? [newOption].concat(_toConsumableArray(propsOptions)) : [].concat(_toConsumableArray(propsOptions), [newOption]) : propsOptions;\n  }, [allowCreateWhileLoading, createOptionPosition, isLoading, newOption, propsOptions]);\n  var onChange = useCallback(function (newValue, actionMeta) {\n    if (actionMeta.action !== 'select-option') {\n      return propsOnChange(newValue, actionMeta);\n    }\n    var valueArray = Array.isArray(newValue) ? newValue : [newValue];\n    if (valueArray[valueArray.length - 1] === newOption) {\n      if (onCreateOption) onCreateOption(inputValue);else {\n        var newOptionData = getNewOptionData(inputValue, inputValue);\n        var newActionMeta = {\n          action: 'create-option',\n          name: name,\n          option: newOptionData\n        };\n        propsOnChange(valueTernary(isMulti, [].concat(_toConsumableArray(cleanValue(value)), [newOptionData]), newOptionData), newActionMeta);\n      }\n      return;\n    }\n    propsOnChange(newValue, actionMeta);\n  }, [getNewOptionData, inputValue, isMulti, name, newOption, onCreateOption, propsOnChange, value]);\n  return _objectSpread(_objectSpread({}, restSelectProps), {}, {\n    options: options,\n    onChange: onChange\n  });\n}\n\nexport { useCreatable as u };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,YAAuB;AACvB,IAAAA,gBAA2B;;;ACC3B,mBAAqC;AAIrC,IAAI,YAAY,CAAC,2BAA2B,wBAAwB,qBAAqB,oBAAoB,oBAAoB,kBAAkB,WAAW,UAAU;AACxK,IAAI,gBAAgB,SAASC,iBAAgB;AAC3C,MAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACrF,MAAI,SAAS,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACnD,MAAI,YAAY,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACtD,MAAI,YAAY,OAAO,UAAU,EAAE,YAAY;AAC/C,MAAI,cAAc,OAAO,UAAU,eAAe,MAAM,CAAC,EAAE,YAAY;AACvE,MAAI,cAAc,OAAO,UAAU,eAAe,MAAM,CAAC,EAAE,YAAY;AACvE,SAAO,gBAAgB,aAAa,gBAAgB;AACtD;AACA,IAAI,WAAW;AAAA,EACb,mBAAmB,SAAS,kBAAkB,YAAY;AACxD,WAAO,WAAY,OAAO,YAAY,GAAI;AAAA,EAC5C;AAAA,EACA,kBAAkB,SAAS,iBAAiB,YAAY,aAAa,eAAe,WAAW;AAC7F,WAAO,EAAE,CAAC,cAAc,YAAY,KAAK,SAAU,QAAQ;AACzD,aAAO,cAAc,YAAY,QAAQ,SAAS;AAAA,IACpD,CAAC,KAAK,cAAc,KAAK,SAAU,QAAQ;AACzC,aAAO,cAAc,YAAY,QAAQ,SAAS;AAAA,IACpD,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,SAAS,iBAAiB,YAAY,aAAa;AACnE,WAAO;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AAAA,EACF;AACF;AACA,SAAS,aAAa,MAAM;AAC1B,MAAI,wBAAwB,KAAK,yBAC/B,0BAA0B,0BAA0B,SAAS,QAAQ,uBACrE,wBAAwB,KAAK,sBAC7B,uBAAuB,0BAA0B,SAAS,SAAS,uBACnE,wBAAwB,KAAK,mBAC7BC,qBAAoB,0BAA0B,SAAS,SAAS,oBAAoB,uBACpF,wBAAwB,KAAK,kBAC7BC,oBAAmB,0BAA0B,SAAS,SAAS,mBAAmB,uBAClF,wBAAwB,KAAK,kBAC7BC,oBAAmB,0BAA0B,SAAS,SAAS,mBAAmB,uBAClF,iBAAiB,KAAK,gBACtB,eAAe,KAAK,SACpB,eAAe,iBAAiB,SAAS,CAAC,IAAI,cAC9C,gBAAgB,KAAK,UACrB,kBAAkB,yBAAyB,MAAM,SAAS;AAC5D,MAAI,wBAAwB,gBAAgB,gBAC1CC,oBAAmB,0BAA0B,SAAS,mBAAiB,uBACvE,yBAAyB,gBAAgB,gBACzCC,oBAAmB,2BAA2B,SAAS,mBAAiB,wBACxE,aAAa,gBAAgB,YAC7B,YAAY,gBAAgB,WAC5B,UAAU,gBAAgB,SAC1B,QAAQ,gBAAgB,OACxB,OAAO,gBAAgB;AACzB,MAAI,gBAAY,sBAAQ,WAAY;AAClC,WAAOH,kBAAiB,YAAY,WAAW,KAAK,GAAG,cAAc;AAAA,MACnE,gBAAgBE;AAAA,MAChB,gBAAgBC;AAAA,IAClB,CAAC,IAAIF,kBAAiB,YAAYF,mBAAkB,UAAU,CAAC,IAAI;AAAA,EACrE,GAAG,CAACA,oBAAmBE,mBAAkBE,mBAAkBD,mBAAkB,YAAYF,mBAAkB,cAAc,KAAK,CAAC;AAC/H,MAAI,cAAU,sBAAQ,WAAY;AAChC,YAAQ,2BAA2B,CAAC,cAAc,YAAY,yBAAyB,UAAU,CAAC,SAAS,EAAE,OAAO,mBAAmB,YAAY,CAAC,IAAI,CAAC,EAAE,OAAO,mBAAmB,YAAY,GAAG,CAAC,SAAS,CAAC,IAAI;AAAA,EACrN,GAAG,CAAC,yBAAyB,sBAAsB,WAAW,WAAW,YAAY,CAAC;AACtF,MAAI,eAAW,0BAAY,SAAU,UAAU,YAAY;AACzD,QAAI,WAAW,WAAW,iBAAiB;AACzC,aAAO,cAAc,UAAU,UAAU;AAAA,IAC3C;AACA,QAAI,aAAa,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAC/D,QAAI,WAAW,WAAW,SAAS,CAAC,MAAM,WAAW;AACnD,UAAI,eAAgB,gBAAe,UAAU;AAAA,WAAO;AAClD,YAAI,gBAAgBC,kBAAiB,YAAY,UAAU;AAC3D,YAAI,gBAAgB;AAAA,UAClB,QAAQ;AAAA,UACR;AAAA,UACA,QAAQ;AAAA,QACV;AACA,sBAAc,aAAa,SAAS,CAAC,EAAE,OAAO,mBAAmB,WAAW,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,aAAa,GAAG,aAAa;AAAA,MACtI;AACA;AAAA,IACF;AACA,kBAAc,UAAU,UAAU;AAAA,EACpC,GAAG,CAACA,mBAAkB,YAAY,SAAS,MAAM,WAAW,gBAAgB,eAAe,KAAK,CAAC;AACjG,SAAO,eAAc,eAAc,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,IAC3D;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;ADzEA,uBAAO;AAKP,IAAI,sBAA+B,0BAAW,SAAU,OAAO,KAAK;AAClE,MAAI,iBAAiB,gBAAgB,KAAK;AAC1C,MAAI,cAAc,aAAa,cAAc;AAC7C,SAA0B,oBAAc,QAAQ,SAAS;AAAA,IACvD;AAAA,EACF,GAAG,WAAW,CAAC;AACjB,CAAC;AACD,IAAI,oBAAoB;", "names": ["import_react", "compareOption", "formatCreateLabel", "isValidNewOption", "getNewOptionData", "getOptionValue$1", "getOptionLabel$1"]}